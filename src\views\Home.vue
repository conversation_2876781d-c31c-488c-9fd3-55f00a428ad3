<template>
    <v-scale-screen width="7552" height="960">
        <!-- width="7552" -->
        <div class="home-container">
            <!-- 头部组件 -->
            <HeaderComponent
                @project-change="handleProjectChange"
            />

            <div class="main-area">

                <div class="content-grid">
                    <div class="content-area grid-1">
                        <GridView />
                    </div>
                    <div class="content-area grid-2">
                        <GridView />
                    </div>
                    <div class="content-area grid-3">
                        <GridView />
                    </div>
                    <div class="content-area grid-4">
                        <GridView />
                    </div>
                </div>
            </div>

         
            
            
        </div>
    </v-scale-screen>
</template>
<script setup>
import VScaleScreen from 'v-scale-screen'
import HeaderComponent from '../components/layout/HeaderComponent.vue'
import GridView from './GridView.vue'

// 定义emit事件，用于向App.vue传递事件
const emit = defineEmits(['project-change'])



// 处理项目切换
const handleProjectChange = (projectId) => {
    console.log('项目切换到:', projectId)
    // 将事件传递给App.vue
    emit('project-change', projectId)

    // 这里可以根据项目切换来更新页面内容或执行其他逻辑
}
</script>

<style lang="less" scoped>
// v-scale-screen 组件不阻挡交互
:deep(.v-screen-box) {
    pointer-events: none !important;
}

.home-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    pointer-events: none; // 不阻挡Three.js交互

    // 头部组件区域需要交互
    :deep(.header-container) {
        pointer-events: auto;
    }

    .main-area {
        flex: 1;
        min-height: 0;
        overflow: hidden;
        pointer-events: none; // 不阻挡Three.js交互
    }

    .content-grid {
        position: relative;
        height: 100%;
        width: 100%;
        box-sizing: border-box;
    }

    .content-area {
        position: absolute;
        background: transparent;
        pointer-events: none; // 不阻挡Three.js交互
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        overflow: hidden;

        // 独立定位类 - 可以单独调整每个GridView的位置
        &.grid-1 {
            top: 0;
            left: 0;
            width: calc(25% - 7.5px);
            height: 100%;
        }

        &.grid-2 {
            top: 0;
            left: calc(25% + 2.5px);
            width: calc(25% - 7.5px);
            height: 100%;
        }

        &.grid-3 {
            top: 0;
            left: calc(50% + 5px);
            width: calc(25% - 7.5px);
            height: 100%;
        }

        &.grid-4 {
            top: 0;
            left: calc(75% + 7.5px);
            width: calc(25% - 7.5px);
            height: 100%;
        }

        // 只有特定的UI组件需要交互
        :deep(.el-button),
        :deep(.el-select),
        :deep(.el-input),
        :deep(.el-dialog),
        :deep(.chart-container),
        :deep(.data-card),
        :deep(.statistics-card) {
            pointer-events: auto;
        }
    }
}
</style>